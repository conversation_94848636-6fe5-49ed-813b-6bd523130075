/**
 * @file Governance Interfaces
 * @filepath shared/src/types/platform/governance/governance-interfaces.ts
 * @task-id G-TSK-01.SUB-01.1.INT-01
 * @component governance-interfaces
 * @reference foundation-context.GOVERNANCE.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24 18:33:55 +03
 * 
 * @description
 * Comprehensive governance interfaces for enterprise rule management system providing:
 * - Rule execution context interfaces for environment management
 * - Validator factory interfaces for rule validation creation
 * - Rule engine core interfaces for central processing
 * - Compliance checker interfaces for governance validation
 * - Authority validator interfaces for permission management
 * - Cache manager interfaces for performance optimization
 * - Metrics collector interfaces for monitoring and analytics
 * - Audit logger interfaces for comprehensive tracking
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-architecture
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.TRACKING.tracking-types
 * @enables governance-rule-management-system, compliance-infrastructure
 * @related-contexts foundation-context, enterprise-context
 * @governance-impact framework-foundation, governance-infrastructure
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/interfaces/governance-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-06-24) - Initial comprehensive governance interfaces with enterprise rule management capabilities
 */

import {
  TTrackingData,
  TValidationResult,
  TGovernanceValidation,
  TAuditResult,
  TGovernanceStatus,
  TGovernanceViolation,
  TAuthorityData,
  TMetrics
} from '../tracking/tracking-types';

import {
  TGovernanceRuleSet,
  TExecutionEnvironment,
  TExecutionContext,
  TRuleExecutionResult,
  TContextStatus,
  TGovernanceRuleType,
  TValidatorConfiguration,
  TGovernanceRule,
  TRuleValidationResult,
  TProcessingContext,
  TRuleProcessingResult,
  TComplianceRequirements,
  TComplianceResult,
  TComplianceScope,
  TComplianceReport,
  TScheduleConfiguration,
  TGovernanceData,
  TGovernanceAction,
  TGovernanceContext,
  TGovernanceComponent,
  TQualityMetrics,
  TAuthorityContext,
  TAuthorityValidationResult,
  TAuthoritySubject,
  TGovernanceOperation,
  TGovernanceResource,
  TPermissionResult,
  TAuthorityHierarchyResult,
  TPermissionSet,
  TCacheStatistics,
  TRuleExecutionMetrics,
  TComplianceMetrics,
  TTimeRange,
  TRulePerformanceMetrics,
  TSystemMetrics,
  TMetricsDashboard,
  TExportFormat,
  TExportResult,
  TGovernanceAuditEvent,
  TRuleExecutionAudit,
  TComplianceCheckAudit,
  TAuthorityValidationAudit,
  TAuditTrailFilters,
  TAuditTrailResult,
  TAuditReportConfig,
  TAuditReport,
  TAuditRetentionPolicy,
  TTestSuite,
  TTestResults,
  TLoadTestConfiguration,
  TLoadTestResults,
  TCoverageReport,
  TCIPipelineConfig,
  TPerformanceTestConfig,
  TPerformanceTestResults,
  TTestHistoryData,
  TTestTrendAnalysis
} from './rule-management-types';

// ============================================================================
// CORE GOVERNANCE SERVICE INTERFACES
// ============================================================================

/**
 * Base governance service interface
 * Foundation for all governance components
 */
export interface IGovernanceService {
  /**
   * Initialize the governance service
   */
  initialize(): Promise<void>;

  /**
   * Validate service state and compliance
   */
  validate(): Promise<TValidationResult>;

  /**
   * Get service metrics and health
   */
  getMetrics(): Promise<TMetrics>;

  /**
   * Check if service is ready
   */
  isReady(): boolean;

  /**
   * Shutdown service gracefully
   */
  shutdown(): Promise<void>;
}

// ============================================================================
// RULE EXECUTION CONTEXT INTERFACES
// ============================================================================

/**
 * Rule execution context interface
 * Manages rule execution environment and state
 */
export interface IGovernanceRuleExecutionContext extends IGovernanceService {
  /**
   * Create execution context for rules
   * @param ruleSet - Rules to execute
   * @param environment - Execution environment
   * @param metadata - Context metadata
   */
  createExecutionContext(
    ruleSet: TGovernanceRuleSet,
    environment: TExecutionEnvironment,
    metadata: Record<string, unknown>
  ): Promise<TExecutionContext>;

  /**
   * Execute rules within context
   * @param contextId - Context identifier
   * @param targetData - Data to validate
   */
  executeRulesInContext(
    contextId: string,
    targetData: Record<string, unknown>
  ): Promise<TRuleExecutionResult>;

  /**
   * Clean up execution context
   * @param contextId - Context to clean up
   */
  cleanupContext(contextId: string): Promise<void>;

  /**
   * Get context status
   * @param contextId - Context identifier
   */
  getContextStatus(contextId: string): Promise<TContextStatus>;
}

// ============================================================================
// RULE VALIDATOR FACTORY INTERFACES
// ============================================================================

/**
 * Rule validator factory interface
 * Creates and manages rule validators
 */
export interface IGovernanceRuleValidatorFactory extends IGovernanceService {
  /**
   * Create validator for rule type
   * @param ruleType - Type of rule to validate
   * @param configuration - Validator configuration
   */
  createValidator(
    ruleType: TGovernanceRuleType,
    configuration: TValidatorConfiguration
  ): Promise<IGovernanceRuleValidator>;

  /**
   * Get available validator types
   */
  getAvailableValidatorTypes(): Promise<TGovernanceRuleType[]>;

  /**
   * Register custom validator
   * @param ruleType - Rule type for validator
   * @param validatorClass - Validator implementation
   */
  registerValidator(
    ruleType: TGovernanceRuleType,
    validatorClass: new() => IGovernanceRuleValidator
  ): Promise<void>;

  /**
   * Remove validator registration
   * @param ruleType - Rule type to remove
   */
  unregisterValidator(ruleType: TGovernanceRuleType): Promise<void>;
}

/**
 * Individual rule validator interface
 */
export interface IGovernanceRuleValidator {
  /**
   * Validate rule against target
   * @param rule - Rule to validate
   * @param target - Target to validate against
   */
  validateRule(rule: TGovernanceRule, target: unknown): Promise<TRuleValidationResult>;

  /**
   * Get supported rule types
   */
  getSupportedTypes(): TGovernanceRuleType[];

  /**
   * Configure validator
   * @param configuration - Validator configuration
   */
  configure(configuration: TValidatorConfiguration): Promise<void>;
}

// ============================================================================
// RULE ENGINE CORE INTERFACES
// ============================================================================

/**
 * Rule engine core interface
 * Central rule processing and management
 */
export interface IGovernanceRuleEngineCore extends IGovernanceService {
  /**
   * Process rule set
   * @param ruleSet - Rules to process
   * @param context - Processing context
   */
  processRuleSet(
    ruleSet: TGovernanceRuleSet,
    context: TProcessingContext
  ): Promise<TRuleProcessingResult>;

  /**
   * Add rule to engine
   * @param rule - Rule to add
   */
  addRule(rule: TGovernanceRule): Promise<void>;

  /**
   * Remove rule from engine
   * @param ruleId - Rule identifier
   */
  removeRule(ruleId: string): Promise<void>;

  /**
   * Update existing rule
   * @param ruleId - Rule identifier
   * @param updates - Rule updates
   */
  updateRule(ruleId: string, updates: Partial<TGovernanceRule>): Promise<void>;

  /**
   * Get rule by ID
   * @param ruleId - Rule identifier
   */
  getRule(ruleId: string): Promise<TGovernanceRule | null>;

  /**
   * Get all rules
   */
  getAllRules(): Promise<TGovernanceRule[]>;
}

// ============================================================================
// COMPLIANCE CHECKER INTERFACES
// ============================================================================

/**
 * Compliance checker interface
 * Validates governance compliance
 */
export interface IComplianceChecker extends IGovernanceService {
  /**
   * Check compliance for target
   * @param target - Target to check
   * @param requirements - Compliance requirements
   */
  checkCompliance(
    target: unknown,
    requirements: TComplianceRequirements
  ): Promise<TComplianceResult>;

  /**
   * Validate governance status
   * @param governanceData - Governance data to validate
   */
  validateGovernanceStatus(governanceData: TGovernanceData): Promise<TGovernanceValidation>;

  /**
   * Generate compliance report
   * @param scope - Report scope
   */
  generateComplianceReport(scope: TComplianceScope): Promise<TComplianceReport>;

  /**
   * Schedule compliance check
   * @param schedule - Check schedule
   * @param requirements - Compliance requirements
   */
  scheduleComplianceCheck(
    schedule: TScheduleConfiguration,
    requirements: TComplianceRequirements
  ): Promise<string>;
}

/**
 * Compliance service interface
 * Extended compliance service capabilities
 */
export interface IComplianceService extends IGovernanceService {
  /**
   * Check compliance for target
   * @param target - Target to check
   * @param requirements - Compliance requirements
   */
  checkCompliance(
    target: unknown,
    requirements: TComplianceRequirements
  ): Promise<TComplianceResult>;
}

/**
 * Compliance framework interface
 * Framework orchestration capabilities
 */
export interface IComplianceFramework extends IGovernanceService {
  /**
   * Orchestrate compliance across contexts
   * @param context - Governance context
   */
  orchestrateCompliance(context: TGovernanceContext): Promise<void>;
}

/**
 * Framework service interface
 * General framework service capabilities
 */
export interface IFrameworkService extends IGovernanceService {
  /**
   * Initialize framework
   */
  initialize(): Promise<void>;
}

/**
 * Quality framework interface
 * Quality assessment capabilities
 */
export interface IQualityFramework extends IGovernanceService {
  /**
   * Assess quality of governance component
   * @param component - Governance component to assess
   */
  assessQuality(component: TGovernanceComponent): Promise<TQualityMetrics>;
}

/**
 * Quality service interface
 * Quality service capabilities
 */
export interface IQualityService extends IGovernanceService {
  /**
   * Assess quality of governance component
   * @param component - Governance component to assess
   */
  assessQuality(component: TGovernanceComponent): Promise<TQualityMetrics>;
}

/**
 * Governance compliance checker interface (legacy)
 * Validates governance compliance
 */
export interface IGovernanceComplianceChecker extends IGovernanceService {
  /**
   * Check compliance for target
   * @param target - Target to check
   * @param requirements - Compliance requirements
   */
  checkCompliance(
    target: unknown,
    requirements: TComplianceRequirements
  ): Promise<TComplianceResult>;

  /**
   * Validate governance status
   * @param governanceData - Governance data to validate
   */
  validateGovernanceStatus(governanceData: TGovernanceData): Promise<TGovernanceValidation>;

  /**
   * Generate compliance report
   * @param scope - Report scope
   */
  generateComplianceReport(scope: TComplianceScope): Promise<TComplianceReport>;

  /**
   * Schedule compliance check
   * @param schedule - Check schedule
   * @param requirements - Compliance requirements
   */
  scheduleComplianceCheck(
    schedule: TScheduleConfiguration,
    requirements: TComplianceRequirements
  ): Promise<string>;
}

// ============================================================================
// AUTHORITY VALIDATOR INTERFACES
// ============================================================================

/**
 * Authority validator interface
 * Validates authority and permissions
 */
export interface IGovernanceAuthorityValidator extends IGovernanceService {
  /**
   * Validate authority for action
   * @param authority - Authority data
   * @param action - Action to validate
   * @param context - Validation context
   */
  validateAuthority(
    authority: TAuthorityData,
    action: TGovernanceAction,
    context: TAuthorityContext
  ): Promise<TAuthorityValidationResult>;

  /**
   * Check permission for operation
   * @param subject - Subject requesting permission
   * @param operation - Operation to check
   * @param resource - Resource being accessed
   */
  checkPermission(
    subject: TAuthoritySubject,
    operation: TGovernanceOperation,
    resource: TGovernanceResource
  ): Promise<TPermissionResult>;

  /**
   * Validate authority hierarchy
   * @param authorities - Authority chain to validate
   */
  validateAuthorityHierarchy(authorities: TAuthorityData[]): Promise<TAuthorityHierarchyResult>;

  /**
   * Get effective permissions
   * @param subject - Subject to get permissions for
   */
  getEffectivePermissions(subject: TAuthoritySubject): Promise<TPermissionSet>;
}

// ============================================================================
// CACHE MANAGER INTERFACES
// ============================================================================

/**
 * Rule cache manager interface
 * Manages rule caching and performance
 */
export interface IGovernanceRuleCacheManager extends IGovernanceService {
  /**
   * Cache rule result
   * @param key - Cache key
   * @param result - Result to cache
   * @param ttl - Time to live
   */
  cacheRuleResult(key: string, result: unknown, ttl?: number): Promise<void>;

  /**
   * Get cached result
   * @param key - Cache key
   */
  getCachedResult<T>(key: string): Promise<T | null>;

  /**
   * Invalidate cache entry
   * @param key - Cache key to invalidate
   */
  invalidateCache(key: string): Promise<void>;

  /**
   * Clear all cache
   */
  clearAllCache(): Promise<void>;

  /**
   * Get cache statistics
   */
  getCacheStatistics(): Promise<TCacheStatistics>;

  /**
   * Optimize cache performance
   */
  optimizeCache(): Promise<void>;
}

// ============================================================================
// METRICS COLLECTOR INTERFACES
// ============================================================================

/**
 * Rule metrics collector interface
 * Collects and manages rule metrics
 */
export interface IGovernanceRuleMetricsCollector extends IGovernanceService {
  /**
   * Record rule execution metric
   * @param ruleId - Rule identifier
   * @param executionData - Execution metrics
   */
  recordRuleExecution(ruleId: string, executionData: TRuleExecutionMetrics): Promise<void>;

  /**
   * Record compliance metric
   * @param complianceData - Compliance metrics
   */
  recordComplianceMetric(complianceData: TComplianceMetrics): Promise<void>;

  /**
   * Get rule performance metrics
   * @param ruleId - Rule identifier
   * @param timeRange - Time range for metrics
   */
  getRulePerformanceMetrics(
    ruleId: string,
    timeRange: TTimeRange
  ): Promise<TRulePerformanceMetrics>;

  /**
   * Get system metrics
   * @param timeRange - Time range for metrics
   */
  getSystemMetrics(timeRange: TTimeRange): Promise<TSystemMetrics>;

  /**
   * Generate metrics dashboard
   */
  generateMetricsDashboard(): Promise<TMetricsDashboard>;

  /**
   * Export metrics data
   * @param format - Export format
   * @param timeRange - Time range
   */
  exportMetrics(format: TExportFormat, timeRange: TTimeRange): Promise<TExportResult>;
}

// ============================================================================
// AUDIT LOGGER INTERFACES
// ============================================================================

/**
 * Rule audit logger interface
 * Comprehensive audit logging
 */
export interface IGovernanceRuleAuditLogger extends IGovernanceService {
  /**
   * Log governance event
   * @param event - Event to log
   */
  logGovernanceEvent(event: TGovernanceAuditEvent): Promise<string>;

  /**
   * Log rule execution
   * @param ruleId - Rule identifier
   * @param execution - Execution details
   */
  logRuleExecution(ruleId: string, execution: TRuleExecutionAudit): Promise<string>;

  /**
   * Log compliance check
   * @param complianceCheck - Compliance check details
   */
  logComplianceCheck(complianceCheck: TComplianceCheckAudit): Promise<string>;

  /**
   * Log authority validation
   * @param authorityValidation - Authority validation details
   */
  logAuthorityValidation(authorityValidation: TAuthorityValidationAudit): Promise<string>;

  /**
   * Get audit trail
   * @param filters - Audit trail filters
   */
  getAuditTrail(filters: TAuditTrailFilters): Promise<TAuditTrailResult>;

  /**
   * Generate audit report
   * @param reportConfig - Report configuration
   */
  generateAuditReport(reportConfig: TAuditReportConfig): Promise<TAuditReport>;

  /**
   * Archive old audit logs
   * @param retentionPolicy - Retention policy
   */
  archiveAuditLogs(retentionPolicy: TAuditRetentionPolicy): Promise<void>;
}

// ============================================================================
// TESTING FRAMEWORK INTERFACES
// ============================================================================

/**
 * Testing framework interface
 */
export interface ITestingFramework extends IGovernanceService {
  /**
   * Generate test suite for governance component
   */
  generateTestSuite(component: TGovernanceComponent): Promise<TTestSuite>;

  /**
   * Execute automated tests
   */
  executeAutomatedTests(testSuite: TTestSuite): Promise<TTestResults>;

  /**
   * Validate rule compliance through testing
   */
  validateRuleCompliance(rules: TGovernanceRule[]): Promise<TTestResults>;

  /**
   * Perform load testing
   */
  performLoadTesting(configuration: TLoadTestConfiguration): Promise<TLoadTestResults>;
}

/**
 * Testing service interface
 */
export interface ITestingService extends IGovernanceService {
  /**
   * Run comprehensive test suite
   */
  runTestSuite(testSuite: TTestSuite): Promise<TTestResults>;

  /**
   * Generate test coverage report
   */
  generateCoverageReport(testResults: TTestResults): Promise<TCoverageReport>;

  /**
   * Integrate with CI/CD pipeline
   */
  integrateCIPipeline(pipelineConfig: TCIPipelineConfig): Promise<void>;

  /**
   * Perform performance testing
   */
  performPerformanceTesting(performanceConfig: TPerformanceTestConfig): Promise<TPerformanceTestResults>;

  /**
   * Analyze test trends
   */
  analyzeTestTrends(historicalData: TTestHistoryData[]): Promise<TTestTrendAnalysis>;
}

// ============================================================================
// RE-EXPORTED TYPES FOR EXTERNAL USE
// ============================================================================

/**
 * Re-export commonly used types from tracking-types for convenience
 */
export type {
  TValidationResult,
  TMetrics,
  TGovernanceValidation,
  TAuditResult,
  TGovernanceStatus,
  TGovernanceViolation,
  TAuthorityData,
  TTrackingData
} from '../tracking/tracking-types';