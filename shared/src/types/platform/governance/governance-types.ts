/**
 * @file Governance Types
 * @filepath shared/src/types/platform/governance/governance-types.ts
 * @task-id M-TSK-01.SUB-04.1.TYP-04
 * @component governance-types
 * @reference foundation-context.TYPES.004
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Type-Definitions
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Governance types module providing:
 * - Type definitions for governance system components
 * - Rule execution metrics and status tracking types
 * - Governance workflow and process type definitions
 * - Enterprise-grade governance type structures
 * - Performance-optimized type definitions for governance operations
 * - Integration type definitions for governance system coordination
 * - Type safety for governance rule execution and monitoring
 * - Comprehensive type coverage for governance functionality
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-012-governance-types-architecture
 * @governance-dcr DCR-foundation-012-governance-types-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @enables server/src/platform/governance/core-managers/GovernanceManager
 * @enables server/src/platform/governance/automation-processing/GovernanceProcessor
 * @related-contexts foundation-context, governance-context, type-definitions-context
 * @governance-impact framework-foundation, governance-system, type-safety
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type type-definitions
 * @lifecycle-stage implementation
 * @testing-status type-checked
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/governance-context/types/governance-types.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial governance types implementation
 * v1.1.0 (2025-07-28) - Added comprehensive type structures for governance system
 */

import { TTimeRange } from '../tracking/tracking-types';

/**
 * Rule execution metrics type
 */
export type TRuleExecutionMetrics = {
  ruleId: string;
  executionTime: number;
  status: 'success' | 'failure' | 'timeout';
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Compliance metrics type
 */
export type TComplianceMetrics = {
  ruleId: string;
  complianceScore: number;
  violations: number;
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Rule performance metrics type
 */
export type TRulePerformanceMetrics = {
  ruleId: string;
  timeRange: TTimeRange;
  executionMetrics: {
    averageExecutionTime: number;
    totalExecutions: number;
    executionTimeData: any[];
  };
  resourceMetrics: {
    averageMemoryUsage: number;
    averageCpuUsage: number;
    memoryUsageData: any[];
    cpuUsageData: any[];
  };
  performanceScore: number;
  recommendations: string[];
};

/**
 * System metrics type
 */
export type TSystemMetrics = {
  timeRange: TTimeRange;
  metrics: {
    ruleExecutions: any;
    ruleResults: any;
    complianceScores: any;
    complianceViolations: any;
    memoryUsage: any;
    cpuUsage: any;
    systemHealth: {
      totalDataPointsCollected: number;
      totalAlertsGenerated: number;
      avgCollectionTimeMs: number;
      errorCount: number;
      activeAlerts: number;
    };
  };
  systemScore: number;
  recommendations: string[];
  summary: {
    totalRuleExecutions: number;
    averageExecutionTime: number;
    systemHealthScore: number;
    activeAlertsCount: number;
  };
};

/**
 * Metrics dashboard type
 */
export type TMetricsDashboard = {
  dashboardId: string;
  title: string;
  description: string;
  timeRange: TTimeRange;
  panels: Array<{
    panelId: string;
    title: string;
    type: 'chart' | 'gauge' | 'table' | 'stat';
    metrics: any[];
    configuration: Record<string, any>;
  }>;
  metadata: Record<string, any>;
};

/**
 * Export format type
 */
export type TExportFormat = 'json' | 'csv' | 'excel' | 'pdf';

/**
 * Export result type
 */
export type TExportResult = {
  exportId: string;
  format: TExportFormat;
  url: string;
  expiresAt: Date;
  metadata: Record<string, any>;
}; 