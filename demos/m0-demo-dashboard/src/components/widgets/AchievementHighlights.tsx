/**
 * ============================================================================
 * AI CONTEXT: AchievementHighlights - M0 Enterprise Achievement Display
 * Purpose: Showcase M0's enterprise achievements and capabilities
 * Complexity: Moderate - Achievement presentation with educational content
 * AI Navigation: 4 sections, achievement display domain
 * Lines: ~250 / Target limit: 300
 * ============================================================================
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Paper,
  Button,
  Collapse,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  EmojiEvents as AchievementIcon,
  TrendingUp as GrowthIcon,
  Security as SecurityIcon,
  Memory as MemoryIcon,
  Speed as PerformanceIcon,
  CheckCircle as CompleteIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Architecture as FoundationIcon,
  School as LearnIcon
} from '@mui/icons-material';
import EducationalTooltip from '../common/EducationalTooltip';
import { achievementEducationalContent } from '../../data/educationalContent';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Achievement display interfaces and types
// ============================================================================

export interface IAchievementHighlightsProps {
  className?: string;
  variant?: 'compact' | 'detailed' | 'showcase';
}

interface IAchievementMetric {
  label: string;
  value: string;
  category: 'completion' | 'quality' | 'security' | 'performance' | 'enterprise';
  description: string;
  icon: React.ReactNode;
}

// ============================================================================
// SECTION 2: ACHIEVEMENT METRICS DATA
// AI Context: M0 enterprise achievement metrics and highlights
// ============================================================================

const achievementMetrics: IAchievementMetric[] = [
  {
    label: 'Completion Achievement',
    value: '129%',
    category: 'completion',
    description: 'Exceeded initial requirements with comprehensive enterprise capabilities',
    icon: <AchievementIcon color="primary" />
  },
  {
    label: 'TypeScript Compilation',
    value: '0 Errors',
    category: 'quality',
    description: 'Perfect TypeScript strict compliance across all components',
    icon: <CompleteIcon color="success" />
  },
  {
    label: 'Enterprise Components',
    value: '35+ Additional',
    category: 'enterprise',
    description: 'Extended beyond core requirements with enterprise-grade features',
    icon: <FoundationIcon color="info" />
  },
  {
    label: 'Memory Improvement',
    value: '98.5%',
    category: 'performance',
    description: 'Dramatic memory efficiency improvement through safe patterns',
    icon: <MemoryIcon color="secondary" />
  },
  {
    label: 'Security Status',
    value: 'Complete Remediation',
    category: 'security',
    description: 'All vulnerabilities addressed with enterprise-grade protection',
    icon: <SecurityIcon color="error" />
  },
  {
    label: 'Bounded Memory Maps',
    value: '48+',
    category: 'performance',
    description: 'Comprehensive memory safety through bounded data structures',
    icon: <PerformanceIcon color="warning" />
  }
];

const getCategoryColor = (category: IAchievementMetric['category']) => {
  switch (category) {
    case 'completion': return 'primary';
    case 'quality': return 'success';
    case 'security': return 'error';
    case 'performance': return 'warning';
    case 'enterprise': return 'info';
    default: return 'default';
  }
};

// ============================================================================
// SECTION 3: MAIN ACHIEVEMENT HIGHLIGHTS COMPONENT
// AI Context: Primary achievement display implementation
// ============================================================================

export default function AchievementHighlights({
  className,
  variant = 'detailed'
}: IAchievementHighlightsProps) {
  const [expanded, setExpanded] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleToggleExpanded = () => setExpanded(!expanded);

  const renderCompactView = () => (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
      {achievementMetrics.slice(0, 3).map((metric, index) => (
        <Chip
          key={index}
          label={`${metric.label}: ${metric.value}`}
          color={getCategoryColor(metric.category) as any}
          variant="outlined"
          size="small"
        />
      ))}
      <Chip
        label={`+${achievementMetrics.length - 3} more`}
        variant="outlined"
        size="small"
        onClick={handleToggleExpanded}
      />
    </Box>
  );

  const renderDetailedView = () => (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
      {achievementMetrics.map((metric, index) => (
        <Box key={index} sx={{ flex: { xs: '1 1 100%', sm: '1 1 45%', md: '1 1 30%' } }}>
          <Card
            sx={{
              height: '100%',
              cursor: 'pointer',
              '&:hover': { elevation: 4 }
            }}
            onClick={() => setSelectedCategory(metric.category)}
          >
            <CardContent sx={{ textAlign: 'center' }}>
              <Box sx={{ mb: 1 }}>
                {metric.icon}
              </Box>
              <Typography variant="h6" color={`${getCategoryColor(metric.category)}.main`}>
                {metric.value}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {metric.label}
              </Typography>
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                {metric.description}
              </Typography>
            </CardContent>
          </Card>
        </Box>
      ))}
    </Box>
  );

  const renderShowcaseView = () => (
    <Paper elevation={2} sx={{ p: 3, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <AchievementIcon fontSize="large" />
        M0 Foundation: Enterprise Achievement Excellence
      </Typography>
      <Typography variant="h6" sx={{ mb: 3, opacity: 0.9 }}>
        Exceeding expectations with 129% completion and enterprise-grade capabilities
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {achievementMetrics.map((metric, index) => (
          <Box key={index} sx={{ flex: { xs: '1 1 100%', sm: '1 1 45%', md: '1 1 30%' } }}>
            <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(255,255,255,0.1)', borderRadius: 2 }}>
              <Box sx={{ mb: 1 }}>
                {React.cloneElement(metric.icon as React.ReactElement, {
                  style: { fontSize: 40, color: 'inherit' }
                })}
              </Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                {metric.value}
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                {metric.label}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Paper>
  );

  return (
    <Box className={className}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <GrowthIcon />
          M0 Enterprise Achievements
        </Typography>
        <EducationalTooltip
          content={achievementEducationalContent.enterpriseReadiness}
          placement="right"
          size="small"
        />
        {variant === 'detailed' && (
          <Button
            startIcon={expanded ? <CollapseIcon /> : <ExpandIcon />}
            onClick={handleToggleExpanded}
            sx={{ ml: 'auto' }}
            size="small"
          >
            {expanded ? 'Show Less' : 'Show Details'}
          </Button>
        )}
      </Box>

      {variant === 'compact' && renderCompactView()}
      {variant === 'detailed' && (
        <>
          {renderDetailedView()}
          <Collapse in={expanded}>
            <Box sx={{ mt: 3 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LearnIcon />
                Technical Implementation Highlights
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><CompleteIcon color="success" /></ListItemIcon>
                  <ListItemText 
                    primary="BaseTrackingService Foundation"
                    secondary="95+ services inherit memory-safe patterns with automatic resource management"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon><SecurityIcon color="error" /></ListItemIcon>
                  <ListItemText 
                    primary="Enterprise Security Implementation"
                    secondary="Complete vulnerability remediation with real-time attack protection"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon><PerformanceIcon color="warning" /></ListItemIcon>
                  <ListItemText 
                    primary="Smart Environment Calculator"
                    secondary="Container-aware resource optimization with dynamic threshold adjustment"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon><FoundationIcon color="info" /></ListItemIcon>
                  <ListItemText 
                    primary="Cross-Reference Validation Engine"
                    secondary="Real-time dependency validation and governance compliance enforcement"
                  />
                </ListItem>
              </List>
            </Box>
          </Collapse>
        </>
      )}
      {variant === 'showcase' && renderShowcaseView()}
    </Box>
  );
}

// ============================================================================
// SECTION 4: ACHIEVEMENT SUMMARY HELPER
// AI Context: Utility functions for achievement data
// ============================================================================

export const getAchievementSummary = () => ({
  totalMetrics: achievementMetrics.length,
  completionRate: '129%',
  qualityScore: '100%',
  enterpriseReady: true,
  securityCompliant: true
});
